"""
Product views module containing the ProductViewSet, ProductImageViewSet, and ProductVariantViewSet classes.
"""
from .base_views import *
from ..utils import vietnamese_search
from ..models import Category, OrderItem
from django.db.models import Subquery, OuterRef, Sum, IntegerField, F
from django.db.models.functions import Coalesce
from ..services.product.product_service import ProductService
from rest_framework.pagination import PageNumberPagination

class ProductPagination(PageNumberPagination):
    """
    Custom pagination class for products.
    """
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class ProductViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing products.
    """
    queryset = Product.objects.filter(is_active=True) # Restored queryset attribute
    serializer_class = ProductSerializer
    pagination_class = ProductPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ['name', 'description', 'code']
    ordering_fields = ['name', 'price', 'created_at', 'total_quantity_sold'] # Added total_quantity_sold
    filterset_fields = ['category', 'is_featured', 'discount_price', 'is_chain']

    def get_queryset(self):
        """
        Get the queryset for products, annotated with total_quantity_sold.
        """
        # print("DEBUG: ProductViewSet.get_queryset() called") # Removed debug print
        base_queryset = Product.objects.filter(is_active=True)

        # Subquery to calculate total quantity sold for each product
        # Only count items from 'delivered' orders that are not deleted.
        total_quantity_sold_subquery = OrderItem.objects.filter(
            product=OuterRef('pk'),
            order__deleted__isnull=True,
            # order__status='delivered'  # Added filter for delivered orders
        ).values('product').annotate(
            total_sold=Sum('quantity')
        ).values('total_sold')

        # Annotate the queryset
        annotated_queryset = base_queryset.annotate(
            total_quantity_sold=Coalesce(
                Subquery(total_quantity_sold_subquery),
                0,
                output_field=IntegerField()
            )
        )
        return annotated_queryset
    
    def get_permissions(self):
        """
        Allow public access to list, retrieve, featured, by_category, search, and promotional.
        Require admin access for other actions.
        """
        if self.action in ['list', 'retrieve', 'featured', 'by_category', 'search', 'promotional', 'best_sellers']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [permissions.IsAdminUser]
        return [permission() for permission in permission_classes]
    
    def get_serializer_context(self):
        """
        Extra context provided to the serializer class.
        Ensures request is available for features like is_favorite.
        """
        context = super().get_serializer_context()
        context.update({"request": self.request})
        return context
    
    @action(detail=False)
    def featured(self, request):
        """
        Get all featured products with pagination.
        """
        # Get the base queryset with annotations from self.get_queryset()
        base_annotated_queryset = self.get_queryset()
        
        # Filter for featured products from the annotated queryset
        featured_queryset = base_annotated_queryset.filter(is_featured=True, is_active=True) # is_active is already handled in get_queryset
        
        # Use pagination
        page = self.paginate_queryset(featured_queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(featured_queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False)
    def by_category(self, request):
        """
        Get products by category id with pagination.
        
        This will return products that:
        1. Directly belong to the specified category
        2. Belong to any descendant subcategory of the specified category
        """
        category_id = request.query_params.get('category_id', None)
        if category_id is None:
            return Response(
                {"error": "Category ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get all category IDs in the hierarchy
        category_ids = self.get_all_descendant_categories(category_id)

        # Get the base queryset with annotations from self.get_queryset()
        base_annotated_queryset = self.get_queryset()
        
        # Filter products by any of these categories from the annotated queryset
        queryset = base_annotated_queryset.filter(
            category_id__in=category_ids
            # is_active=True is already handled in get_queryset
        )
        
        # Use pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    def get_all_descendant_categories(self, parent_id):
        """
        Recursively get all descendant category IDs for a given parent category.
        
        Args:
            parent_id: The ID of the parent category
            
        Returns:
            A list of category IDs including the parent and all descendants
        """
        result = [int(parent_id)]
        direct_children = Category.objects.filter(parent_id=parent_id)
        
        for child in direct_children:
            result.extend(self.get_all_descendant_categories(child.id))
            
        return result


    @action(detail=False)
    def promotional(self, request):
        """
        Get all products with discount_price not null and less than the original price.

        This endpoint returns products that are currently on promotion/discount.
        """
        # Get base annotated queryset
        annotated_base_queryset = self.get_queryset()

        # Filter for products with discount_price not null and less than price
        promotional_queryset = annotated_base_queryset.filter(
            discount_price__isnull=False,
            discount_price__lt=F('price')  # Ensure discount_price is less than price
        )

        # Use pagination
        page = self.paginate_queryset(promotional_queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(promotional_queryset, many=True)
        return Response(serializer.data)

    @action(detail=False)
    def best_sellers(self, request):
        """
        Get best seller products based on total quantity sold.

        Query parameters:
        - startDate: Filter orders from this date (YYYY-MM-DD format)
        - endDate: Filter orders to this date (YYYY-MM-DD format)
        - category: Filter by category ID
        - limit: Number of products to return (default: 12)
        """

        # Get query parameters (handle both DRF and Django request objects)
        query_params = getattr(request, 'query_params', request.GET)
        start_date = query_params.get('startDate')
        end_date = query_params.get('endDate')
        category_id = query_params.get('category')
        limit = query_params.get('limit', 12)

        # Get best sellers using service
        queryset, error = ProductService.get_best_sellers(
            start_date=start_date,
            end_date=end_date,
            category_id=category_id,
            limit=limit
        )

        if error:
            return Response(
                {'error': error},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Serialize the results
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False)
    def search(self, request):
        """
        Search products by query string with Vietnamese accent-insensitive matching.
        
        This search will match products based on:
        1. Original text with accents (e.g. "Cà phê")
        2. Text without accents (e.g. "Ca phe")
        
        This allows users to search using Vietnamese with or without accent marks.
        
        The search covers product name, description, and category name.
        """
        query = request.query_params.get('q', None)
        if query is None:
            return Response(
                {"error": "Query parameter 'q' is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get base annotated queryset
        annotated_base_queryset = self.get_queryset()
        
        # Perform Vietnamese accent-insensitive search on product name, description, and code
        # using the annotated queryset as the base.
        product_fields = ['name', 'description', 'code']
        products_match = vietnamese_search(annotated_base_queryset, product_fields, query)
        
        # Search in category names
        category_match_query = vietnamese_search(
            Category.objects.all(),
            ['name'],
            query
        )
        # Get products related to matching categories, ensuring they are from the annotated set
        category_products_match = annotated_base_queryset.filter(category__in=category_match_query)
        
        # Combine results and remove duplicates
        # The resulting queryset will retain the annotations.
        queryset = (products_match | category_products_match).distinct()
        
        # Use pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class ProductImageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing product images.
    """
    queryset = ProductImage.objects.all()
    serializer_class = ProductImageSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):  # Handle schema generation
            return ProductImage.objects.none()
            
        # Filter by product if product_id is provided
        product_id = self.request.query_params.get('product_id')
        if product_id:
            return ProductImage.objects.filter(product_id=product_id)
        return ProductImage.objects.all()
    
    @swagger_auto_schema(
        operation_description="Set an image as the primary image for a product",
        responses={200: ProductImageSerializer}
    )
    @action(detail=True, methods=['post'])
    def set_primary(self, request, pk=None):
        """
        Set this image as the primary image for its product.
        """
        image = self.get_object()
        
        # Reset primary status for all images of this product
        ProductImage.objects.filter(product=image.product).update(is_primary=False)
        
        # Set this image as primary
        image.is_primary = True
        image.save()
        
        return Response(ProductImageSerializer(image).data)

class ProductVariantViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing product variants.
    """
    queryset = ProductVariant.objects.filter(is_active=True)
    serializer_class = ProductVariantSerializer
    permission_classes = [permissions.IsAdminUser]

    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):  # Handle schema generation
            return ProductVariant.objects.none()
            
        # Filter by product if product_id is provided
        product_id = self.request.query_params.get('product_id')
        if product_id:
            return ProductVariant.objects.filter(product_id=product_id, is_active=True)
        return ProductVariant.objects.filter(is_active=True)

class VariantImageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing variant images.
    """
    queryset = VariantImage.objects.all()
    serializer_class = VariantImageSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):  # Handle schema generation
            return VariantImage.objects.none()
            
        # Filter by variant if variant_id is provided
        variant_id = self.request.query_params.get('variant_id')
        if variant_id:
            return VariantImage.objects.filter(variant_id=variant_id)
        return VariantImage.objects.all()
    
    @swagger_auto_schema(
        operation_description="Set an image as the primary image for a variant",
        responses={200: VariantImageSerializer}
    )
    @action(detail=True, methods=['post'])
    def set_primary(self, request, pk=None):
        """
        Set this image as the primary image for its variant.
        """
        image = self.get_object()
        
        # Reset primary status for all images of this variant
        VariantImage.objects.filter(variant=image.variant).update(is_primary=False)
        
        # Set this image as primary
        image.is_primary = True
        image.save()
        
        return Response(VariantImageSerializer(image).data)
