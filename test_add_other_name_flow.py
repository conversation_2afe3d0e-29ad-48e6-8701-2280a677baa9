#!/usr/bin/env python3
"""
Test script để mô phỏng flow của AddOtherNameForm
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzg5MDMxNDEyLCJpYXQiOjE3NTc0OTU0MTIsImp0aSI6IjY1ZDk0YWRiZWZlNzQ5OTVhMjkwZTZiZjhiZWY0Y2FlIiwidXNlcl9pZCI6MX0.YxGKGYbvvqSv1k4y7dun_0b6iuTWQIUtoMQjqRemEZA"

def test_add_other_name_flow():
    """Test flow giống như AddOtherNameForm"""
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Test data - sử dụng sản phẩm đã test trước đó
    product_code = None  # Sẽ lấy sản phẩm đầu tiên có code
    new_other_name = "Tên khác từ OCR Test"
    
    print(f"🧪 Testing AddOtherNameForm flow...")
    print(f"Product Code: {product_code}")
    print(f"New Other Name: {new_other_name}")
    
    try:
        # Step 1: L<PERSON>y danh sách sản phẩm và chọn sản phẩm có code để test
        print("\n1️⃣ Getting products list...")
        search_url = f"{BASE_URL}/products/?page_size=10"
        response = requests.get(search_url, headers=headers)

        if response.status_code != 200:
            print(f"❌ Get products failed: {response.status_code} - {response.text}")
            return

        search_data = response.json()
        products = search_data.get('results', [])

        # Tìm sản phẩm có code
        product = None
        for p in products:
            if p.get('code'):
                product = p
                product_code = p.get('code')
                break

        if not product:
            print(f"❌ No product with code found")
            print(f"Available products: {[p.get('name', 'No name')[:20] for p in products[:3]]}")
            return
        
        print(f"✅ Found product: {product['name']} (ID: {product['id']})")
        
        # Step 2: Lấy chi tiết sản phẩm (giống get product details)
        print("\n2️⃣ Getting product details...")
        detail_url = f"{BASE_URL}/products/{product['id']}/"
        response = requests.get(detail_url, headers=headers)
        
        if response.status_code != 200:
            print(f"❌ Get details failed: {response.status_code} - {response.text}")
            return
        
        product_details = response.json()
        current_other_names = product_details.get('other_name', [])
        print(f"✅ Current other_names: {current_other_names}")
        
        # Step 3: Kiểm tra duplicate (giống checkDuplicateOtherName)
        print("\n3️⃣ Checking for duplicates...")
        if new_other_name in current_other_names:
            print(f"⚠️ Other name '{new_other_name}' already exists")
            return
        
        print(f"✅ No duplicate found")
        
        # Step 4: Update other_name (giống updateProductOtherNames)
        print("\n4️⃣ Updating other_name...")
        updated_other_names = current_other_names + [new_other_name]
        
        update_data = {
            "other_name": updated_other_names
        }
        
        response = requests.patch(detail_url, json=update_data, headers=headers)
        
        if response.status_code == 200:
            updated_product = response.json()
            print(f"✅ Update successful!")
            print(f"Updated other_names: {updated_product.get('other_name', [])}")
        else:
            print(f"❌ Update failed: {response.status_code}")
            print(f"Response: {response.text}")
            
        # Step 5: Verify update
        print("\n5️⃣ Verifying update...")
        response = requests.get(detail_url, headers=headers)
        if response.status_code == 200:
            verified_product = response.json()
            verified_other_names = verified_product.get('other_name', [])
            print(f"✅ Verified other_names: {verified_other_names}")
            
            if new_other_name in verified_other_names:
                print(f"🎉 SUCCESS: AddOtherNameForm flow completed successfully!")
            else:
                print(f"❌ FAILED: New other name not found in verified data")
        else:
            print(f"❌ Verification failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")

if __name__ == "__main__":
    test_add_other_name_flow()
