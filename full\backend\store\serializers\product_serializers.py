"""
Product-related serializers
"""
from rest_framework import serializers
from ..models import Product, ProductImage, ProductVariant, VariantImage

class ProductImageSerializer(serializers.ModelSerializer):
    """Serializer for ProductImage model"""
    class Meta:
        model = ProductImage
        fields = ['id', 'product', 'image', 'alt_text', 'is_primary', 'created_at']
        read_only_fields = ['id', 'created_at']

class VariantImageSerializer(serializers.ModelSerializer):
    """Serializer for VariantImage model"""
    class Meta:
        model = VariantImage
        fields = ['id', 'variant', 'image', 'alt_text', 'is_primary', 'created_at']
        read_only_fields = ['id', 'created_at']

class ProductVariantSerializer(serializers.ModelSerializer):
    """Serializer for ProductVariant model"""
    images = VariantImageSerializer(many=True, read_only=True)
    main_image = serializers.ImageField(read_only=True)
    
    class Meta:
        model = ProductVariant
        fields = ['id', 'product', 'name', 'sku', 'price', 'discount_price',
                  'stock', 'unit', 'is_active', 'max_quantities_in_cart', 'created_at', 'updated_at', 'images', 'main_image']
        read_only_fields = ['id', 'created_at', 'updated_at']

class ProductSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    images = ProductImageSerializer(many=True, read_only=True)
    variants = ProductVariantSerializer(many=True, read_only=True)
    image_url = serializers.SerializerMethodField()
    is_favorite = serializers.SerializerMethodField()
    main_image = serializers.ImageField(read_only=True)
    total_quantity_sold = serializers.IntegerField(read_only=True)

    class Meta:
        model = Product
        fields = ['id', 'name', 'description', 'price', 'chain_price', 'discount_price',
                 'category', 'category_name', 'stock', 'image', 'image_url',
                 'is_featured', 'is_active', 'max_quantities_in_cart', 'created_at', 'updated_at',
                 'images', 'variants', 'is_favorite', 'unit', 'code', 'main_image',
                 'specifications', 'weight', 'total_quantity_sold', 'is_chain', 'other_name']
        read_only_fields = ['id', 'created_at', 'updated_at', 'main_image', 'total_quantity_sold']
        
    def get_image_url(self, obj):
        if obj.main_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.main_image.url)
            # Fallback to relative URL if no request is available
            return obj.main_image.url
        return None
        
    def get_is_favorite(self, obj):
        request = self.context.get('request')
        print("🚀 ~ request:", request.user.is_authenticated)
        if request and request.user.is_authenticated:
            return obj.favorited_by.filter(user=request.user).exists()
        return False
