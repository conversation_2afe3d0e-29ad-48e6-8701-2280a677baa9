# Export Utilities

This module contains utilities for exporting order data to Excel format.

## Overview

The export utilities provide functionality to export both single orders and multiple orders to Excel files with specific formatting requirements for accounting systems.

## Key Components

### singleOrderExportUtils.ts

Handles exporting individual order details to Excel format with the following features:

- **Order Information Export**: Exports selected order fields based on user selection
- **Product Details Export**: Exports detailed product information including discount column
- **Order Type Support**: Different formats for regular orders vs showroom orders

#### Column Structure

**Regular Orders:**
| STT | Mã hàng | Tên sản phẩm | Phân loại | Số lượng | Đơn giá | Chiết khấu | Thành tiền |
|-----|---------|--------------|-----------|----------|---------|------------|------------|

**Showroom Orders:**
| STT | Mã hàng | Tên sản phẩm | <PERSON>ân loại | Số lượng | Kh<PERSON>i lượng (kg) |
|-----|---------|--------------|-----------|----------|-----------------|

#### Recent Updates
- **Added "Chiết khấu" (Discount) column** between "Đơn giá" and "Thành tiền"
- Uses `chiet_khau_amount` field from OrderItem model
- Handles null/undefined discount values by displaying 0
- Updated total row positioning to accommodate new column

### multiOrderExportUtils.ts

Handles the export of multiple orders to Excel format with the following features:

- **Product-level rows**: Each product in an order gets its own row
- **Customer information repetition**: Customer details are repeated for each product row within the same order
- **Shipping fee handling**: Orders with shipping fees get an additional row for the shipping cost
- **Tax calculations**: Automatic tax calculation based on order tax rate (shows "-" for 0% tax)
- **Fixed account codes**: Uses predefined accounting codes from constants

#### Column Mapping

The export follows specific column mapping requirements:

| Excel Column | Data Source | Notes |
|-------------|-------------|-------|
| Số chứng từ* | Order ID | Document number |
| Ngày hạch toán* | Order created_at | Accounting date (DD/MM/YYYY) |
| Ngoại tệ | Fixed: "VNĐ" | Currency |
| Trạng thái HĐĐT | Fixed: "1" | Invoice status |
| Mã khách hàng | Fixed: "KL-018" | Customer code |
| Người nhận | Order customer name | Recipient |
| Địa chỉ | Order shipping address | Address |
| Email | Order email | Customer email |
| Tài khoản nợ | Fixed: "131101" | Debt account |
| Mã sản phẩm | Product code or "PGH" for shipping | Product/service code |
| Tên sản phẩm | Product name or "Phí giao hàng" | Product/service name |
| DVT | Product unit or "lan" for shipping | Unit of measure |
| Mã kho | Fixed: "DTT" | Warehouse code |
| Số lượng | Product quantity or 1 for shipping | Quantity |
| Giá bán | Product price or shipping fee | Unit price |
| Thành tiền | Total amount (quantity × price) | Total amount |
| Thuế suất | Order tax rate (%) | Tax rate |
| Thuế | Calculated tax amount or "-" | Tax amount |
| TK thuế có | Fixed: "333111" | Tax payable account |
| TK kho | Fixed: "1561" | Inventory account |

#### Shipping Fee Handling

When an order has a shipping fee, an additional row is added with:
- Mã sản phẩm: "PGH"
- Tên sản phẩm: "Phí giao hàng"
- DVT: "lan"
- Số lượng: 1
- Thuế suất: 0 (no tax on shipping)
- TK doanh thu: "5113"
- TK giá vốn: "6323"

### Functions

#### `generateNewMultiOrderExportData(orders: Order[])`
Main function that processes orders and returns Excel-ready data.

#### `createProductItemRow(order: Order, item: OrderItem)`
Creates a data row for a product item within an order.

#### `createShippingFeeRow(order: Order)`
Creates a data row for shipping fees when applicable.

#### `calculateTaxAmount(taxRate: number, amount: number)`
Calculates tax amounts based on rate and base amount.

## Usage Example

```typescript
import { generateNewMultiOrderExportData } from '@/utils/export/multiOrderExportUtils';

const orders = [/* array of Order objects */];
const exportData = generateNewMultiOrderExportData(orders);

// Use exportData with Excel export library
```

## Constants

Fixed values are defined in `@/constants/constants.ts` under `MULTI_ORDER_EXPORT_FIXED_VALUES`:

- Currency: "VNĐ"
- Invoice status: "1"
- Customer code: "KL-018"
- Debt account: "131101"
- Warehouse code: "DTT"
- Tax payable account: "333111"
- Inventory account: "1561"
- Shipping product code: "PGH"
- Shipping service name: "Phí giao hàng"
- Shipping unit: "lần"
- Shipping revenue account: "5113"
- Shipping cost account: "6323"

## Data Flow

```mermaid
flowchart TD
    A[Orders Array] --> B[For each Order]
    B --> C[For each Product Item]
    C --> D[Create Product Row]
    D --> E[Add to Export Data]
    E --> F{Has Shipping Fee?}
    F -->|Yes| G[Create Shipping Row]
    F -->|No| H[Next Order]
    G --> I[Add Shipping to Export Data]
    I --> H
    H --> J[Return Export Data]
```

## Edge Cases Handled

1. **Missing product codes**: Uses empty string if not available
2. **Missing product units**: Uses empty string if not available
3. **Zero tax rates**: Shows "-" instead of 0 for tax amount
4. **Missing customer information**: Uses empty strings for missing fields
5. **Zero shipping fees**: No shipping row is added
6. **Product variants**: Combines product name with variant name in format "Product (Variant)"

## Type Safety

The module uses TypeScript interfaces:
- `Order`: Main order structure
- `OrderItem`: Individual product items within orders
- All functions include proper type annotations for parameters and return values
