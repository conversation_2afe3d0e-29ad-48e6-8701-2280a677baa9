#!/usr/bin/env python3
"""
Test script để kiểm tra toàn bộ flow AddOtherNameForm
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzg5MDMxNDEyLCJpYXQiOjE3NTc0OTU0MTIsImp0aSI6IjY1ZDk0YWRiZWZlNzQ5OTVhMjkwZTZiZjhiZWY0Y2FlIiwidXNlcl9pZCI6MX0.YxGKGYbvvqSv1k4y7dun_0b6iuTWQIUtoMQjqRemEZA"

def test_complete_flow():
    """Test toàn bộ flow AddOtherNameForm"""
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Test với mã sản phẩm có sẵn
    product_code = "XXXX001"
    new_other_name = "Tên khác từ AddOtherNameForm Test"
    
    print(f"🧪 Testing complete AddOtherNameForm flow...")
    print(f"Product Code: {product_code}")
    print(f"New Other Name: {new_other_name}")
    
    try:
        # Step 1: Search product by code (giống findProductByCode)
        print(f"\n1️⃣ Searching for product by code: {product_code}")
        search_url = f"{BASE_URL}/products/?search={product_code}&page_size=10"
        response = requests.get(search_url, headers=headers)
        
        if response.status_code != 200:
            print(f"❌ Search failed: {response.status_code} - {response.text}")
            return
        
        search_data = response.json()
        products = search_data.get('results', [])
        
        # Find exact match
        product = None
        for p in products:
            if p.get('code') == product_code:
                product = p
                break
        
        if not product:
            print(f"❌ Product with code '{product_code}' not found")
            return
        
        print(f"✅ Found product: {product['name']} (ID: {product['id']})")
        
        # Step 2: Get product details (giống get current product details)
        print(f"\n2️⃣ Getting product details for ID: {product['id']}")
        detail_url = f"{BASE_URL}/products/{product['id']}/"
        response = requests.get(detail_url, headers=headers)
        
        if response.status_code != 200:
            print(f"❌ Get details failed: {response.status_code} - {response.text}")
            return
        
        product_details = response.json()
        current_other_names = product_details.get('other_name', [])
        print(f"✅ Current other_names: {current_other_names}")
        
        # Step 3: Check for duplicates
        print(f"\n3️⃣ Checking for duplicates...")
        if new_other_name in current_other_names:
            print(f"⚠️ Other name '{new_other_name}' already exists")
            return
        
        print(f"✅ No duplicate found")
        
        # Step 4: Update other_name with PATCH
        print(f"\n4️⃣ Updating other_name with PATCH...")
        updated_other_names = current_other_names + [new_other_name]
        
        patch_data = {
            "other_name": updated_other_names
        }
        
        print(f"PATCH URL: {detail_url}")
        print(f"PATCH Data: {patch_data}")
        
        response = requests.patch(detail_url, json=patch_data, headers=headers)
        
        print(f"PATCH Response Status: {response.status_code}")
        
        if response.status_code == 200:
            updated_product = response.json()
            print(f"✅ PATCH successful!")
            print(f"Updated other_names: {updated_product.get('other_name', [])}")
        else:
            print(f"❌ PATCH failed: {response.status_code}")
            print(f"Response: {response.text}")
            return
            
        # Step 5: Verify with GET
        print(f"\n5️⃣ Verifying with GET...")
        response = requests.get(detail_url, headers=headers)
        if response.status_code == 200:
            verified_product = response.json()
            verified_other_names = verified_product.get('other_name', [])
            print(f"✅ Verified other_names: {verified_other_names}")
            
            if new_other_name in verified_other_names:
                print(f"🎉 SUCCESS: Complete AddOtherNameForm flow works perfectly!")
                print(f"✅ Search by code: WORKING")
                print(f"✅ Get product details: WORKING") 
                print(f"✅ PATCH update: WORKING")
                print(f"✅ Verification: WORKING")
            else:
                print(f"❌ FAILED: New other name not found in verified data")
        else:
            print(f"❌ Verification failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_flow()
