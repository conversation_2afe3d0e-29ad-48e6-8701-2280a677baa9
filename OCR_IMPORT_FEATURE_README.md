# OCR Import Feature Documentation

## Overview
This feature enhances the Order Import page with OCR (Optical Character Recognition) capabilities using Google Gemini 2.5 Flash Lite API. Users can now upload images of orders and automatically extract product information.

## Features Implemented

### 1. Backend Enhancements

#### Product Model Updates
- ✅ Added `other_name` field to Product model (JSONField)
- ✅ Updated Product serializer to include `other_name`
- ✅ Enhanced Product admin interface with custom widget for managing other names

#### OCR API Endpoint
- ✅ Created `/api/order-ocr/` endpoint
- ✅ Integrated with Google Gemini 2.5 Flash Lite API
- ✅ Handles image upload and returns structured JSON data
- ✅ Error handling and validation

### 2. Frontend Enhancements

#### New Components
- ✅ `ImageUploadSection.tsx` - Handles image upload and OCR processing
- ✅ `ProductMatchingSection.tsx` - Matches OCR results with existing products
- ✅ Enhanced `OrderImportActions.tsx` - Updated UI with tabbed interface
- ✅ Updated `OrderImportPage.tsx` - Integrated all new components

#### Key Features
- ✅ Image upload with drag & drop support
- ✅ Real-time OCR processing
- ✅ Product matching with confidence levels
- ✅ Ability to add other names for products
- ✅ Visual feedback and error handling
- ✅ Responsive design

## API Usage

### OCR Endpoint
```bash
curl --location 'http://127.0.0.1:8000/api/order-ocr/' \
--form 'image=@"path/to/image.jpg"'
```

**Response:**
```json
{
  "products": [
    {
      "product": "Tên sản phẩm",
      "quantity": 2
    }
  ]
}
```

### Product Matching Logic
The system uses a multi-level matching approach:

1. **High Confidence**: Exact name match (product name or other_name)
2. **Medium Confidence**: Contains match (partial string matching)
3. **Low Confidence**: Partial word match
4. **No Match**: No suitable product found

## Setup Instructions

### 1. Backend Setup
1. Ensure `GEMINI_API_KEY` is set in Django settings
2. Run migrations to add `other_name` field:
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

### 2. Frontend Setup
1. Install dependencies:
   ```bash
   cd admin
   pnpm install
   ```

2. Build the project:
   ```bash
   pnpm build
   ```

### 3. Testing
1. Start the backend server:
   ```bash
   cd full/backend
   python manage.py runserver
   ```

2. Test the OCR API:
   ```bash
   python test_ocr_api.py
   ```

## Usage Guide

### For Administrators

#### Adding Other Names to Products
1. Go to Admin → Products
2. Edit a product
3. In the "Thông tin cơ bản" section, find the "Other Name" field
4. Enter alternative names, one per line
5. Save the product

#### Using OCR Import
1. Go to Orders → Import
2. Click on "Import từ Hình ảnh (OCR)" tab
3. Upload an image of the order
4. Click "Xử lý OCR" to process the image
5. Review the matched products
6. Add other names for unmatched products if needed

### For Developers

#### Extending Product Matching
The matching logic can be enhanced in `ProductMatchingSection.tsx`:

```typescript
const findBestMatch = (productName: string) => {
  // Add custom matching logic here
  // Return: { product: Product | null, confidence: string }
}
```

#### Customizing OCR Prompt
Modify the prompt in `full/backend/store/services/gemini.py`:

```python
prompt = "please read the image and return only following json: \n[{\"product\": \"[tên_sản_phẩm]\", \"quantity\": [so_luong]}]"
```

## File Structure

```
admin/src/
├── components/orders/Import/
│   ├── ImageUploadSection.tsx      # Image upload & OCR processing
│   ├── ProductMatchingSection.tsx  # Product matching logic
│   ├── OrderImportActions.tsx      # Updated actions component
│   ├── OrderImportInstructions.tsx # Instructions component
│   └── OrderImportTable.tsx        # Data table component
├── pages/orders/
│   └── import.tsx                  # Main import page
└── components/ui/
    └── alert.tsx                   # Alert component

full/backend/
├── store/
│   ├── models/product.py           # Product model with other_name
│   ├── admin/product_admin.py      # Enhanced admin interface
│   ├── serializers/product_serializers.py # Updated serializer
│   ├── views/order_ocr_views.py    # OCR API endpoint
│   └── services/gemini.py          # Gemini API integration
```

## Error Handling

### Common Issues
1. **OCR API Key Missing**: Ensure `GEMINI_API_KEY` is set in settings
2. **Image Upload Fails**: Check file size (max 10MB) and format
3. **No Products Found**: Verify product names and other_name entries
4. **Build Errors**: Ensure all dependencies are installed

### Debugging
- Check browser console for frontend errors
- Check Django logs for backend errors
- Use the test script to verify OCR API functionality

## Future Enhancements

### Planned Features
- [ ] Batch image processing
- [ ] Advanced product matching algorithms
- [ ] Export OCR results to Excel
- [ ] Integration with order creation workflow
- [ ] Support for multiple languages
- [ ] Image preprocessing for better OCR accuracy

### Performance Optimizations
- [ ] Image compression before OCR
- [ ] Caching of product matching results
- [ ] Async processing for large images
- [ ] Database indexing for faster searches

## Support

For issues or questions:
1. Check the error logs
2. Verify API key configuration
3. Test with the provided test script
4. Review the component documentation

## Changelog

### Version 1.0.0
- ✅ Initial OCR import feature implementation
- ✅ Product other_name field support
- ✅ Enhanced admin interface
- ✅ Product matching with confidence levels
- ✅ Responsive UI design
