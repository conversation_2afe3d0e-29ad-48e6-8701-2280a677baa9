// src/hooks/useImageUpload.ts
import { useState, useRef } from "react";
import { useToast } from "@/context/toast-hooks";
import { apiCall, endpoints } from "@/lib/api";
import { OCRProduct } from "@/types/product";

export const useImageUpload = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [ocrResult, setOcrResult] = useState<OCRProduct[] | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showToast } = useToast();

  const validateFile = (file: File): boolean => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      showToast('Vui lòng chọn file hình ảnh', 'error');
      return false;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      showToast('File quá lớn. Vui lòng chọn file nhỏ hơn 10MB', 'error');
      return false;
    }

    return true;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && validateFile(file)) {
      setSelectedFile(file);
      setError(null);
      setOcrResult(null);

      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (validateFile(file)) {
        setSelectedFile(file);
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);
        setError(null);
        setOcrResult(null);
      }
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const removeFile = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setOcrResult(null);
    setError(null);
    
    // Clean up object URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  const processOCR = async (): Promise<OCRProduct[]> => {
    if (!selectedFile) {
      throw new Error('Không có file được chọn');
    }

    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('image', selectedFile);

      const response = await apiCall<{ products: OCRProduct[] }>(
        'POST',
        endpoints.orders.ocr,
        formData,
        { isFormData: true }
      );

      if (response.products && Array.isArray(response.products)) {
        setOcrResult(response.products);
        showToast(`Đã xử lý thành công ${response.products.length} sản phẩm từ hình ảnh`, 'success');
        return response.products;
      } else {
        throw new Error('Dữ liệu OCR không hợp lệ');
      }
    } catch (error: any) {
      console.error('OCR Error:', error);
      const errorMessage = error.response?.data?.error || error.message || 'Có lỗi xảy ra khi xử lý hình ảnh';
      setError(errorMessage);
      showToast(errorMessage, 'error');
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  const clearAll = () => {
    removeFile();
    setOcrResult(null);
    setError(null);
  };

  return {
    // State
    selectedFile,
    previewUrl,
    isProcessing,
    ocrResult,
    error,
    fileInputRef,
    
    // Actions
    handleFileSelect,
    handleDrop,
    handleDragOver,
    removeFile,
    processOCR,
    clearAll,
  };
};
