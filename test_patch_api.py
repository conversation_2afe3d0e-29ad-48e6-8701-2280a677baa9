#!/usr/bin/env python3
"""
Test script để kiểm tra PATCH API trực tiếp
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzg5MDMxNDEyLCJpYXQiOjE3NTc0OTU0MTIsImp0aSI6IjY1ZDk0YWRiZWZlNzQ5OTVhMjkwZTZiZjhiZWY0Y2FlIiwidXNlcl9pZCI6MX0.YxGKGYbvvqSv1k4y7dun_0b6iuTWQIUtoMQjqRemEZA"

def test_patch_api():
    """Test PATCH API trực tiếp"""
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # 1. <PERSON><PERSON><PERSON> một sản phẩm để test
        print("1️⃣ Getting a product to test...")
        products_url = f"{BASE_URL}/products/?page_size=1"
        response = requests.get(products_url, headers=headers)
        
        if response.status_code != 200:
            print(f"❌ Failed to get products: {response.status_code} - {response.text}")
            return
        
        products_data = response.json()
        if not products_data.get('results'):
            print("❌ No products found")
            return
        
        product = products_data['results'][0]
        product_id = product['id']
        product_name = product['name']
        current_other_names = product.get('other_name', [])
        
        print(f"✅ Testing with product: {product_name} (ID: {product_id})")
        print(f"Current other_names: {current_other_names}")
        
        # 2. Test PATCH request
        print("\n2️⃣ Testing PATCH request...")
        patch_url = f"{BASE_URL}/products/{product_id}/"
        new_other_name = "Test PATCH API Direct"
        updated_other_names = current_other_names + [new_other_name]
        
        patch_data = {
            "other_name": updated_other_names
        }
        
        print(f"PATCH URL: {patch_url}")
        print(f"PATCH Data: {patch_data}")
        print(f"Headers: {headers}")
        
        response = requests.patch(patch_url, json=patch_data, headers=headers)
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            updated_product = response.json()
            print("✅ PATCH successful!")
            print(f"Updated other_names: {updated_product.get('other_name', [])}")
        else:
            print(f"❌ PATCH failed: {response.status_code}")
            print(f"Response Text: {response.text}")
            
            # Try to parse error response
            try:
                error_data = response.json()
                print(f"Error JSON: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error response as JSON")
        
        # 3. Verify with GET
        print("\n3️⃣ Verifying with GET...")
        get_response = requests.get(patch_url, headers=headers)
        if get_response.status_code == 200:
            verified_product = get_response.json()
            print(f"Verified other_names: {verified_product.get('other_name', [])}")
        else:
            print(f"❌ GET verification failed: {get_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Testing PATCH API directly...")
    test_patch_api()
