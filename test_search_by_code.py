#!/usr/bin/env python3
"""
Test script để kiểm tra search API với mã sản phẩm
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzg5MDMxNDEyLCJpYXQiOjE3NTc0OTU0MTIsImp0aSI6IjY1ZDk0YWRiZWZlNzQ5OTVhMjkwZTZiZjhiZWY0Y2FlIiwidXNlcl9pZCI6MX0.YxGKGYbvvqSv1k4y7dun_0b6iuTWQIUtoMQjqRemEZA"

def test_search_by_code():
    """Test search API với mã sản phẩm"""
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # 1. <PERSON><PERSON><PERSON> một sản phẩm có code để test
        print("1️⃣ Getting a product with code to test...")
        products_url = f"{BASE_URL}/products/?page_size=10"
        response = requests.get(products_url, headers=headers)
        
        if response.status_code != 200:
            print(f"❌ Failed to get products: {response.status_code} - {response.text}")
            return
        
        products_data = response.json()
        if not products_data.get('results'):
            print("❌ No products found")
            return
        
        # Tìm sản phẩm có code
        test_product = None
        for product in products_data['results']:
            if product.get('code'):
                test_product = product
                break
        
        if not test_product:
            print("❌ No product with code found")
            return
        
        product_code = test_product['code']
        product_name = test_product['name']
        
        print(f"✅ Found product to test: {product_name} (Code: {product_code})")
        
        # 2. Test search với mã sản phẩm
        print(f"\n2️⃣ Testing search with product code: {product_code}")
        search_url = f"{BASE_URL}/products/?search={product_code}&page_size=10"
        
        print(f"Search URL: {search_url}")
        
        response = requests.get(search_url, headers=headers)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            search_results = response.json()
            results = search_results.get('results', [])
            
            print(f"✅ Search successful! Found {len(results)} results")
            
            # Kiểm tra xem có tìm thấy sản phẩm đúng không
            found_exact_match = False
            for result in results:
                print(f"  - {result.get('name', 'No name')} (Code: {result.get('code', 'No code')})")
                if result.get('code') == product_code:
                    found_exact_match = True
            
            if found_exact_match:
                print(f"🎉 SUCCESS: Found exact match for product code '{product_code}'!")
            else:
                print(f"⚠️ WARNING: No exact match found for product code '{product_code}'")
                
        else:
            print(f"❌ Search failed: {response.status_code}")
            print(f"Response: {response.text}")
        
        # 3. Test với một mã không tồn tại
        print(f"\n3️⃣ Testing search with non-existent code...")
        fake_code = "NONEXISTENT123"
        search_url = f"{BASE_URL}/products/?search={fake_code}&page_size=10"
        
        response = requests.get(search_url, headers=headers)
        
        if response.status_code == 200:
            search_results = response.json()
            results = search_results.get('results', [])
            print(f"✅ Search with fake code returned {len(results)} results (expected: 0)")
        else:
            print(f"❌ Search with fake code failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Testing Product Search by Code...")
    test_search_by_code()
