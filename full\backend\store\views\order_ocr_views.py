from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>ars<PERSON>, FormParser
from ..serializers import OrderOCRSerializer
from ..services.gemini import get_ocr_from_image

class OrderOCRView(APIView):
    """
    API endpoint to upload an image of an order, process it with Gemini OCR,
    and return the raw OCR result without DB matching.
    """
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        serializer = OrderOCRSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        image_file = serializer.validated_data['image']

        try:
            ocr_data = get_ocr_from_image(image_file)
            if not ocr_data or not isinstance(ocr_data, list):
                return Response(
                    {"error": "Failed to parse OCR data from the image."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Trả về raw kết quả từ Gemini
        return Response({
            "products": ocr_data
        }, status=status.HTTP_200_OK)
