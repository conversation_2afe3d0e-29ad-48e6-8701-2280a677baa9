#!/usr/bin/env python3
"""
Test script to verify total_price calculation logic
"""
from decimal import Decimal

def test_total_price_calculation():
    # Test case from the user's example
    custom_total_price = Decimal('15000')
    chiet_khau_amount = Decimal('10000')
    quantity = 1
    
    # Calculate unit price as per the formula
    price = (custom_total_price + chiet_khau_amount) / Decimal(quantity)
    price = price.quantize(Decimal('0.01'))
    
    print(f"Input:")
    print(f"  custom_total_price: {custom_total_price}")
    print(f"  chiet_khau_amount: {chiet_khau_amount}")
    print(f"  quantity: {quantity}")
    print(f"")
    print(f"Calculated:")
    print(f"  unit price: {price}")
    print(f"  final total_price (should be custom_total_price): {custom_total_price}")
    print(f"")
    print(f"Expected behavior:")
    print(f"  - Order total should be: {custom_total_price}")
    print(f"  - Item total_price should be: {custom_total_price}")
    print(f"  - Item price should be: {price}")
    print(f"  - Item chiet_khau_amount should be: {chiet_khau_amount}")

if __name__ == "__main__":
    test_total_price_calculation()
