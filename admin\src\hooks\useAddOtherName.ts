// src/hooks/useAddOtherName.ts
import { useState } from "react";
import { useToast } from "@/context/toast-hooks";
import { apiCall, endpoints } from "@/lib/api";
import { Product } from "@/types/product";

interface UseAddOtherNameProps {
  initialOtherName?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const useAddOtherName = ({ 
  initialOtherName = '', 
  onSuccess, 
  onCancel 
}: UseAddOtherNameProps = {}) => {
  const [productCode, setProductCode] = useState('');
  const [otherName, setOtherName] = useState(initialOtherName);
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();

  const validateInputs = (): boolean => {
    if (!productCode.trim()) {
      showToast('Vui lòng nhập mã sản phẩm', 'error');
      return false;
    }

    if (!otherName.trim()) {
      showToast('Vui lòng nhập tên khác cho sản phẩm', 'error');
      return false;
    }

    return true;
  };

  const findProductByCode = async (code: string): Promise<Product | null> => {
    try {
      const searchResponse = await apiCall<{ results: Product[] }>(
        'GET',
        `${endpoints.products.list}?search=${encodeURIComponent(code)}&page_size=10`
      );

      // Find exact match by code
      const product = searchResponse.results?.find(p => p.code === code.trim());
      return product || null;
    } catch (error) {
      console.error('Error finding product by code:', error);
      return null;
    }
  };

  const checkDuplicateOtherName = (product: Product, newOtherName: string): boolean => {
    const currentOtherNames = product.other_name || [];
    return currentOtherNames.includes(newOtherName.trim());
  };

  const updateProductOtherNames = async (productId: number, newOtherNames: string[]): Promise<void> => {
    await apiCall(
      'PATCH',
      endpoints.products.update(productId),
      { other_name: newOtherNames }
    );
  };

  const submitOtherName = async (): Promise<boolean> => {
    if (!validateInputs()) {
      return false;
    }

    setIsLoading(true);
    try {
      // Find the product by code
      const product = await findProductByCode(productCode);
      
      if (!product) {
        showToast(`Không tìm thấy sản phẩm với mã "${productCode}"`, 'error');
        return false;
      }

      // Get current product details to ensure we have the latest other_name array
      const productResponse = await apiCall<Product>(
        'GET',
        endpoints.products.detail(product.id)
      );

      // Check if other name already exists
      if (checkDuplicateOtherName(productResponse, otherName)) {
        showToast('Tên khác này đã tồn tại cho sản phẩm', 'warning');
        return false;
      }

      // Add the new other name
      const currentOtherNames = productResponse.other_name || [];
      const updatedOtherNames = [...currentOtherNames, otherName.trim()];
      
      await updateProductOtherNames(product.id, updatedOtherNames);

      showToast(
        `Đã thêm tên khác "${otherName.trim()}" cho sản phẩm "${product.name}" (${product.code})`, 
        'success'
      );
      
      // Reset form
      resetForm();
      
      // Call success callback
      onSuccess?.();
      
      return true;
    } catch (error: any) {
      console.error('Error adding other name:', error);
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.error || 
                          'Không thể thêm tên khác cho sản phẩm';
      showToast(errorMessage, 'error');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setProductCode('');
    setOtherName(initialOtherName);
  };

  const cancelForm = () => {
    resetForm();
    onCancel?.();
  };

  const updateProductCode = (code: string) => {
    setProductCode(code.toUpperCase());
  };

  const updateOtherName = (name: string) => {
    setOtherName(name);
  };

  // Update other name when initialOtherName changes
  const setInitialOtherName = (name: string) => {
    setOtherName(name);
  };

  return {
    // State
    productCode,
    otherName,
    isLoading,
    
    // Actions
    updateProductCode,
    updateOtherName,
    submitOtherName,
    resetForm,
    cancelForm,
    setInitialOtherName,
    
    // Computed
    canSubmit: !isLoading && productCode.trim() !== '' && otherName.trim() !== '',
  };
};
