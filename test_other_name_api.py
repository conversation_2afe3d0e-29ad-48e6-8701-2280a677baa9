#!/usr/bin/env python3
"""
Test script để kiểm tra API update other_name của Product
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"
ADMIN_USERNAME = "admin"  # Thay đổi theo username admin của bạn
ADMIN_PASSWORD = "admin"  # Thay đổi theo password admin của bạn

def get_auth_token():
    """Lấy token xác thực"""
    # Sử dụng token được cung cấp
    return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzg5MDMxNDEyLCJpYXQiOjE3NTc0OTU0MTIsImp0aSI6IjY1ZDk0YWRiZWZlNzQ5OTVhMjkwZTZiZjhiZWY0Y2FlIiwidXNlcl9pZCI6MX0.YxGKGYbvvqSv1k4y7dun_0b6iuTWQIUtoMQjqRemEZA"

def test_product_other_name_update():
    """Test update other_name của product"""
    # Lấy token
    token = get_auth_token()
    if not token:
        print("Cannot get auth token. Exiting...")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 1. L<PERSON>y danh sách products để tìm một product để test
    products_url = f"{BASE_URL}/products/"
    try:
        response = requests.get(products_url, headers=headers)
        if response.status_code != 200:
            print(f"Failed to get products: {response.status_code} - {response.text}")
            return
        
        products_data = response.json()
        if not products_data.get('results'):
            print("No products found")
            return
        
        # Lấy product đầu tiên để test
        test_product = products_data['results'][0]
        product_id = test_product['id']
        product_name = test_product['name']
        current_other_names = test_product.get('other_name', [])
        
        print(f"Testing with product: {product_name} (ID: {product_id})")
        print(f"Current other_names: {current_other_names}")
        
        # 2. Test update other_name
        new_other_name = "Test Other Name từ API"
        updated_other_names = current_other_names + [new_other_name]
        
        update_url = f"{BASE_URL}/products/{product_id}/"
        update_data = {
            "other_name": updated_other_names
        }
        
        print(f"Updating other_name to: {updated_other_names}")
        
        response = requests.patch(update_url, json=update_data, headers=headers)
        
        if response.status_code == 200:
            updated_product = response.json()
            print("✅ Update successful!")
            print(f"Updated other_names: {updated_product.get('other_name', [])}")
        else:
            print(f"❌ Update failed: {response.status_code}")
            print(f"Response: {response.text}")
            
        # 3. Verify by getting product details
        response = requests.get(update_url, headers=headers)
        if response.status_code == 200:
            product_details = response.json()
            print(f"Verified other_names: {product_details.get('other_name', [])}")
        else:
            print(f"Failed to verify: {response.status_code}")
            
    except Exception as e:
        print(f"Error during test: {e}")

if __name__ == "__main__":
    print("🧪 Testing Product other_name API update...")
    test_product_other_name_update()
