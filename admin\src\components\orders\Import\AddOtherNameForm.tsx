// src/components/orders/Import/AddOtherNameForm.tsx
import React, { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";
import { useAddOtherName } from "@/hooks/useAddOtherName";

interface AddOtherNameFormProps {
  ocrProductName: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export const AddOtherNameForm: React.FC<AddOtherNameFormProps> = ({
  ocrProductName,
  onSuccess,
  onCancel,
}) => {
  const {
    productCode,
    otherName,
    isLoading,
    updateProductCode,
    updateOtherName,
    submitOtherName,
    cancelForm,
    setInitialOtherName,
    canSubmit,
  } = useAddOtherName({
    initialOtherName: ocrProductName,
    onSuccess,
    onCancel,
  });

  // Update other name when ocrProductName changes
  useEffect(() => {
    setInitialOtherName(ocrProductName);
  }, [ocrProductName, setInitialOtherName]);

  const handleSubmit = async () => {
    await submitOtherName();
  };

  const handleCancel = () => {
    cancelForm();
  };

  return (
    <div className="space-y-3 mt-3 p-4 bg-gray-50 rounded-lg border">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">
          Thêm tên khác cho sản phẩm
        </h4>
        <Button
          onClick={handleCancel}
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>
      
      <div className="space-y-2">
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Mã sản phẩm <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={productCode}
            onChange={(e) => updateProductCode(e.target.value)}
            placeholder="Ví dụ: SP001, ABC123..."
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Tên khác <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={otherName}
            onChange={(e) => updateOtherName(e.target.value)}
            placeholder="Tên khác cho sản phẩm..."
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="flex gap-2 pt-2">
        <Button
          onClick={handleSubmit}
          disabled={!canSubmit}
          size="sm"
          className="flex-1"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Đang xử lý...
            </>
          ) : (
            <>
              <Check className="w-4 h-4 mr-2" />
              Thêm tên khác
            </>
          )}
        </Button>
        <Button
          onClick={handleCancel}
          variant="outline"
          size="sm"
          disabled={isLoading}
        >
          Hủy
        </Button>
      </div>
    </div>
  );
};
