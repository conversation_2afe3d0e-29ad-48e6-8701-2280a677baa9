// src/components/orders/Import/ImageUploadSection.tsx
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, Image as ImageIcon, X, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { OCRProduct } from "@/types/product";

interface ImageUploadSectionProps {
  // Image upload states
  selectedFile: File | null;
  previewUrl: string | null;
  isProcessing: boolean;
  ocrResult: OCRProduct[] | null;
  error: string | null;
  fileInputRef: React.RefObject<HTMLInputElement>;

  // Image upload actions
  handleFileSelect: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleDrop: (event: React.DragEvent) => void;
  handleDragOver: (event: React.DragEvent) => void;
  removeFile: () => void;

  // OCR actions
  onOCRProcess: () => Promise<void>;
  onClearOCR: () => void;
}

export const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({
  selectedFile,
  previewUrl,
  isProcessing,
  ocrResult,
  error,
  fileInputRef,
  handleFileSelect,
  handleDrop,
  handleDragOver,
  removeFile,
  onOCRProcess,
  onClearOCR,
}) => {

  const handleRemoveFile = () => {
    removeFile();
    onClearOCR();
  };

  const handleOCRProcess = async () => {
    await onOCRProcess();
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ImageIcon className="w-5 h-5" />
          Upload hình ảnh đơn hàng (OCR)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!selectedFile ? (
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">
              Kéo thả hình ảnh vào đây hoặc click để chọn file
            </p>
            <Button
              onClick={() => fileInputRef.current?.click()}
              variant="outline"
            >
              Chọn hình ảnh
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>
        ) : (
          <div className="space-y-4">
            {/* Image Preview */}
            <div className="relative">
              <img
                src={previewUrl || ''}
                alt="Preview"
                className="w-full max-w-md mx-auto rounded-lg border"
                style={{ maxHeight: '300px', objectFit: 'contain' }}
              />
              <Button
                onClick={handleRemoveFile}
                variant="destructive"
                size="sm"
                className="absolute top-2 right-2"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* OCR Actions */}
            <div className="flex gap-2">
              <Button
                onClick={handleOCRProcess}
                disabled={isProcessing}
                className="flex-1"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Đang xử lý OCR...
                  </>
                ) : (
                  'Xử lý OCR'
                )}
              </Button>
              <Button
                onClick={handleRemoveFile}
                variant="outline"
              >
                Chọn file khác
              </Button>
            </div>

            {/* Error Display */}
            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
                <AlertCircle className="w-5 h-5" />
                <span>{error}</span>
              </div>
            )}

            {/* Success Display */}
            {ocrResult && ocrResult.length > 0 && (
              <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
                <CheckCircle className="w-5 h-5" />
                <span>Đã xử lý thành công {ocrResult.length} sản phẩm</span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};