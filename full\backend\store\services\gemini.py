import requests
import base64
import json
from django.conf import settings
from django.core.files.uploadedfile import InMemoryUploadedFile

def get_ocr_from_image(image_file: InMemoryUploadedFile):
    """
    Sends an image to the Google Gemini API for OCR and returns the parsed JSON response.
    """
    api_key = settings.GEMINI_API_KEY
    if not api_key:
        raise ValueError("GEMINI_API_KEY is not set in Django settings.")

    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent"
    
    # Read image content and encode it in base64
    image_content = image_file.read()
    encoded_image = base64.b64encode(image_content).decode('utf-8')

    prompt = "please read the image and return only following json: \n[{\"product\": \"[tên_sản_phẩm]\", \"quantity\": [so_luong]}]"

    payload = {
        "contents": [
            {
                "parts": [
                    {"text": prompt},
                    {
                        "inline_data": {
                            "mime_type": image_file.content_type,
                            "data": encoded_image
                        }
                    }
                ]
            }
        ]
    }

    headers = {
        'Content-Type': 'application/json'
    }

    params = {
        'key': api_key
    }

    try:
        response = requests.post(url, headers=headers, params=params, json=payload, timeout=60)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        
        response_json = response.json()
        
        # Extract the text content from the response
        if 'candidates' in response_json and len(response_json['candidates']) > 0:
            content_part = response_json['candidates'][0]['content']['parts'][0]
            if 'text' in content_part:
                # The response text might be wrapped in markdown json block
                text_content = content_part['text'].strip()
                if text_content.startswith("```json"):
                    text_content = text_content[7:]
                if text_content.endswith("```"):
                    text_content = text_content[:-3]
                
                # Parse the cleaned text content as JSON
                return json.loads(text_content)
        
        return None

    except requests.exceptions.RequestException as e:
        # Handle network-related errors
        print(f"Error calling Gemini API: {e}")
        return None
    except json.JSONDecodeError as e:
        # Handle errors in parsing the response from Gemini
        print(f"Error decoding JSON from Gemini API response: {e}")
        print(f"Raw response: {response.text}")
        return None
    except (KeyError, IndexError) as e:
        # Handle unexpected response structure
        print(f"Unexpected response structure from Gemini API: {e}")
        print(f"Raw response: {response_json}")
        return None

