// src/components/orders/Import/ProductMatchingSection.tsx
import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "antd";
import { Search, Plus, Check, X, AlertTriangle } from "lucide-react";
import { useProductMatching } from "@/hooks/useProductMatching";
import { OCRProduct, MatchedProduct } from "@/types/product";
import { AddOtherNameForm } from "./AddOtherNameForm";

interface ProductMatchingSectionProps {
  ocrProducts: OCRProduct[];
  onMatchedProducts: (matchedProducts: MatchedProduct[]) => void;
}

export const ProductMatchingSection: React.FC<ProductMatchingSectionProps> = ({
  ocrProducts,
  onMatchedProducts,
}) => {
  const [showAddOtherName, setShowAddOtherName] = useState<number | null>(null);

  const { matchedProducts, isLoading, reprocessMatching } = useProductMatching(ocrProducts);

  // Update parent component when matched products change
  useEffect(() => {
    onMatchedProducts(matchedProducts);
  }, [matchedProducts, onMatchedProducts]);

  const handleAddOtherNameSuccess = () => {
    setShowAddOtherName(null);
    // Re-process matching after adding other name
    reprocessMatching();
  };

  const handleAddOtherNameCancel = () => {
    setShowAddOtherName(null);
  };

  const getConfidenceBadgeColor = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'success';
      case 'medium': return 'warning';
      case 'low': return 'error';
      default: return 'default';
    }
  };

  const getConfidenceText = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'Khớp cao';
      case 'medium': return 'Khớp trung bình';
      case 'low': return 'Khớp thấp';
      default: return 'Không tìm thấy';
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="w-5 h-5" />
          Kết quả tìm kiếm sản phẩm
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Đang tải danh sách sản phẩm...</p>
          </div>
        ) : (
          <div className="space-y-3">
            {matchedProducts.map((match, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-lg">{match.ocrProduct.product}</span>
                    <Badge
                      color={getConfidenceBadgeColor(match.confidence)}
                      text={getConfidenceText(match.confidence)}
                    />
                    <span className="text-sm text-gray-500">
                      Số lượng: {match.ocrProduct.quantity}
                    </span>
                  </div>
                </div>

                {match.matchedProduct ? (
                  <div className="flex items-center gap-2 text-green-600">
                    <Check className="w-4 h-4" />
                    <span>Tìm thấy: {match.matchedProduct.name}</span>
                    {match.matchedProduct.code && (
                      <span className="text-sm text-gray-500">({match.matchedProduct.code})</span>
                    )}
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-red-600">
                      <X className="w-4 h-4" />
                      <span>Không tìm thấy sản phẩm phù hợp</span>
                    </div>

                    {showAddOtherName === index ? (
                      <AddOtherNameForm
                        ocrProductName={match.ocrProduct.product}
                        onSuccess={handleAddOtherNameSuccess}
                        onCancel={handleAddOtherNameCancel}
                      />
                    ) : (
                      <Button
                        onClick={() => setShowAddOtherName(index)}
                        variant="outline"
                        size="sm"
                        className="mt-2"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Thêm tên khác cho sản phẩm
                      </Button>
                    )}
                  </div>
                )}

                {match.confidence === 'low' && match.matchedProduct && (
                  <div className="flex items-center gap-2 text-yellow-600 mt-2">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="text-sm">Độ khớp thấp, vui lòng kiểm tra lại</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};