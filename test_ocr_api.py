#!/usr/bin/env python3
"""
Test script for the OCR API endpoint
"""
import requests
import json
import os

def test_ocr_api():
    """Test the OCR API endpoint"""
    api_url = "http://127.0.0.1:8000/api/order-ocr/"
    
    # Test with a sample image (you'll need to provide a real image file)
    test_image_path = "test_image.jpg"  # Replace with actual image path
    
    if not os.path.exists(test_image_path):
        print(f"❌ Test image not found: {test_image_path}")
        print("Please provide a test image file named 'test_image.jpg' in the current directory")
        return False
    
    try:
        with open(test_image_path, 'rb') as image_file:
            files = {'image': image_file}
            response = requests.post(api_url, files=files, timeout=60)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ OCR API Response:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing OCR API...")
    success = test_ocr_api()
    if success:
        print("✅ OCR API test completed successfully!")
    else:
        print("❌ OCR API test failed!")
